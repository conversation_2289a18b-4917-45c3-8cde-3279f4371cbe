#!/usr/bin/env ts-node

/**
 * 数据库初始化脚本
 * 检查并创建所有必要的数据库表
 */

import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import config from '../src/config';
import logger from '../src/config/logger';

interface TableInfo {
  TABLE_NAME: string;
  TABLE_COMMENT: string;
}

/**
 * 数据库初始化类
 */
class DatabaseInitializer {
  private connection: mysql.Connection | null = null;

  /**
   * 创建数据库连接
   */
  async connect(): Promise<void> {
    try {
      this.connection = await mysql.createConnection({
        host: config.database.host,
        port: config.database.port,
        user: config.database.user,
        password: config.database.password,
        charset: config.database.charset,
        timezone: '+08:00',
      });

      logger.info('Database connection established');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * 创建数据库（如果不存在）
   */
  async createDatabase(): Promise<void> {
    if (!this.connection) {
      throw new Error('Database connection not established');
    }

    try {
      const createDbSql = `CREATE DATABASE IF NOT EXISTS \`${config.database.name}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`;
      await this.connection.execute(createDbSql);
      
      // 切换到目标数据库
      await this.connection.execute(`USE \`${config.database.name}\``);
      
      logger.info(`Database '${config.database.name}' created/verified successfully`);
    } catch (error) {
      logger.error('Failed to create database:', error);
      throw error;
    }
  }

  /**
   * 获取当前数据库中的所有表
   */
  async getExistingTables(): Promise<string[]> {
    if (!this.connection) {
      throw new Error('Database connection not established');
    }

    try {
      const [rows] = await this.connection.execute<mysql.RowDataPacket[]>(
        'SHOW TABLES'
      );
      
      return rows.map(row => Object.values(row)[0] as string);
    } catch (error) {
      logger.error('Failed to get existing tables:', error);
      throw error;
    }
  }

  /**
   * 获取表的详细信息
   */
  async getTableInfo(): Promise<TableInfo[]> {
    if (!this.connection) {
      throw new Error('Database connection not established');
    }

    try {
      const [rows] = await this.connection.execute<mysql.RowDataPacket[]>(
        `SELECT TABLE_NAME, TABLE_COMMENT 
         FROM INFORMATION_SCHEMA.TABLES 
         WHERE TABLE_SCHEMA = ? 
         ORDER BY TABLE_NAME`,
        [config.database.name]
      );
      
      return rows as TableInfo[];
    } catch (error) {
      logger.error('Failed to get table info:', error);
      throw error;
    }
  }

  /**
   * 执行 SQL 脚本
   */
  async executeSqlScript(scriptPath: string): Promise<void> {
    if (!this.connection) {
      throw new Error('Database connection not established');
    }

    try {
      const sqlContent = fs.readFileSync(scriptPath, 'utf8');
      
      // 分割 SQL 语句（简单的分割，基于分号）
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      logger.info(`Executing ${statements.length} SQL statements from ${scriptPath}`);

      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await this.connection.execute(statement);
          } catch (error: any) {
            // 忽略 "table already exists" 错误
            if (!error.message.includes('already exists')) {
              logger.warn(`SQL statement failed: ${statement.substring(0, 100)}...`);
              logger.warn(`Error: ${error.message}`);
            }
          }
        }
      }

      logger.info('SQL script executed successfully');
    } catch (error) {
      logger.error('Failed to execute SQL script:', error);
      throw error;
    }
  }

  /**
   * 验证必要的表是否存在
   */
  async validateTables(): Promise<{ missing: string[], existing: string[] }> {
    const requiredTables = [
      'admins',
      'users', 
      'sessions',
      'operation_logs',
      'phone_categories',
      'memory_specs',
      'price_tags',
      'price_history',
      'price_trend_rankings',
      'price_trend_history',
      'sync_records',
      'miniprogram_configs',
      'miniprogram_config_logs',
      'miniprogram_contacts',
      'contact_share_configs',
      'contact_access_logs'
    ];

    const existingTables = await this.getExistingTables();
    const missing = requiredTables.filter(table => !existingTables.includes(table));
    const existing = requiredTables.filter(table => existingTables.includes(table));

    return { missing, existing };
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.connection) {
      await this.connection.end();
      this.connection = null;
      logger.info('Database connection closed');
    }
  }

  /**
   * 主初始化流程
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Starting database initialization...');

      // 1. 连接数据库
      await this.connect();

      // 2. 创建数据库
      await this.createDatabase();

      // 3. 检查现有表
      const { missing, existing } = await this.validateTables();
      
      logger.info(`Found ${existing.length} existing tables: ${existing.join(', ')}`);
      
      if (missing.length > 0) {
        logger.info(`Missing ${missing.length} tables: ${missing.join(', ')}`);
        
        // 4. 执行初始化脚本
        const scriptPath = path.join(__dirname, 'init-database.sql');
        if (fs.existsSync(scriptPath)) {
          await this.executeSqlScript(scriptPath);
          logger.info('Database initialization script executed');
        } else {
          logger.error(`SQL script not found: ${scriptPath}`);
          throw new Error('Database initialization script not found');
        }

        // 5. 再次验证
        const { missing: stillMissing } = await this.validateTables();
        if (stillMissing.length > 0) {
          logger.error(`Still missing tables after initialization: ${stillMissing.join(', ')}`);
          throw new Error('Database initialization incomplete');
        }
      }

      // 6. 显示最终状态
      const tableInfo = await this.getTableInfo();
      logger.info('Database initialization completed successfully');
      logger.info(`Total tables: ${tableInfo.length}`);
      
      console.log('\n=== Database Tables ===');
      tableInfo.forEach(table => {
        console.log(`✓ ${table.TABLE_NAME} - ${table.TABLE_COMMENT || 'No comment'}`);
      });

    } catch (error) {
      logger.error('Database initialization failed:', error);
      throw error;
    } finally {
      await this.close();
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const initializer = new DatabaseInitializer();
  
  try {
    await initializer.initialize();
    console.log('\n✅ Database initialization completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Database initialization failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { DatabaseInitializer };
