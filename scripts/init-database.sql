-- 数据库初始化脚本
-- 创建所有必要的表结构
-- 执行时间: 2025-06-22

USE baofeng_recycle;

-- 1. 管理员表
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '管理员ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(100) COMMENT '邮箱',
    full_name VARCHAR(100) COMMENT '姓名',
    role ENUM('SUPER_ADMIN', 'ADMIN', 'OPERATOR') DEFAULT 'ADMIN' COMMENT '角色',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 2. 用户表（简化版）
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY COMMENT '用户ID（使用微信OpenID）',
    phone VARCHAR(20) COMMENT '手机号码',
    avatar VARCHAR(500) COMMENT '头像URL',
    role ENUM('ADMIN', 'USER') DEFAULT 'USER' COMMENT '用户角色',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    wechat_open_id VARCHAR(100) NOT NULL UNIQUE COMMENT '微信OpenID',
    wechat_union_id VARCHAR(100) COMMENT '微信UnionID',
    wechat_session_key VARCHAR(100) COMMENT '微信会话密钥',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_phone (phone),
    INDEX idx_wechat_open_id (wechat_open_id),
    INDEX idx_wechat_union_id (wechat_union_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 3. 会话表
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(128) PRIMARY KEY COMMENT '会话ID',
    user_id VARCHAR(50) COMMENT '用户ID',
    admin_id INT COMMENT '管理员ID',
    user_type ENUM('USER', 'ADMIN') NOT NULL COMMENT '用户类型',
    session_data JSON COMMENT '会话数据',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_user_type (user_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表';

-- 4. 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_type ENUM('USER', 'ADMIN', 'SYSTEM') NOT NULL COMMENT '用户类型',
    user_id VARCHAR(50) COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource VARCHAR(100) COMMENT '操作资源',
    resource_id VARCHAR(50) COMMENT '资源ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    status ENUM('SUCCESS', 'FAILED', 'PENDING') DEFAULT 'SUCCESS' COMMENT '操作状态',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_type (user_type),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 5. 手机分类表
CREATE TABLE IF NOT EXISTS phone_categories (
    id INT PRIMARY KEY COMMENT '分类ID，直接使用API返回的id',
    parent_id INT DEFAULT 0 COMMENT '父级ID',
    level TINYINT NOT NULL COMMENT '分类级别：0-根分类，1-品牌，2-型号，3-子型号',
    name VARCHAR(200) NOT NULL COMMENT '分类名称',
    sort_index INT DEFAULT 0 COMMENT '排序索引',
    remake TEXT COMMENT '备注信息',
    wechat_tag JSON COMMENT '微信标签信息',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_sort_index (sort_index)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='手机分类表';

-- 6. 内存规格表
CREATE TABLE IF NOT EXISTS memory_specs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID，关联phone_categories表',
    memory_size VARCHAR(20) NOT NULL COMMENT '内存大小，如：64GB, 128GB',
    memory_type VARCHAR(20) DEFAULT 'ROM' COMMENT '内存类型：ROM, RAM',
    sort_index INT DEFAULT 0 COMMENT '排序索引',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_category_memory (category_id, memory_size, memory_type),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内存规格表';

-- 7. 价格标签表
CREATE TABLE IF NOT EXISTS price_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID',
    memory_spec_id INT COMMENT '内存规格ID',
    price_rate DECIMAL(10,2) NOT NULL COMMENT '价格倍率',
    group_name VARCHAR(100) NOT NULL COMMENT '分组名称',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    description TEXT COMMENT '描述信息',
    sort_index INT DEFAULT 0 COMMENT '排序索引',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_id (category_id),
    INDEX idx_memory_spec_id (memory_spec_id),
    INDEX idx_group_name (group_name),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格标签表';

-- 8. 价格历史表
CREATE TABLE IF NOT EXISTS price_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID',
    memory_spec_id INT COMMENT '内存规格ID',
    price_rate DECIMAL(10,2) NOT NULL COMMENT '价格倍率',
    price_change DECIMAL(10,2) DEFAULT 0 COMMENT '价格变化',
    change_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '变化百分比',
    record_date DATE NOT NULL COMMENT '记录日期',
    data_source VARCHAR(50) DEFAULT 'API' COMMENT '数据来源',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_category_memory_date (category_id, memory_spec_id, record_date),
    INDEX idx_category_id (category_id),
    INDEX idx_record_date (record_date),
    INDEX idx_price_change (price_change)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格历史表';

-- 9. 价格趋势排名表
CREATE TABLE IF NOT EXISTS price_trend_rankings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID',
    category_name VARCHAR(200) NOT NULL COMMENT '分类名称',
    current_price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
    price_change DECIMAL(10,2) NOT NULL COMMENT '价格变化',
    change_percentage DECIMAL(5,2) NOT NULL COMMENT '变化百分比',
    trend_type ENUM('RISE', 'FALL') NOT NULL COMMENT '趋势类型',
    consecutive_rise_days INT DEFAULT 0 COMMENT '连续上涨天数',
    consecutive_fall_days INT DEFAULT 0 COMMENT '连续下跌天数',
    trend_signal VARCHAR(20) DEFAULT NULL COMMENT '趋势信号',
    ranking_position INT NOT NULL COMMENT '排名位置',
    record_date DATE NOT NULL COMMENT '记录日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_category_trend_date (category_id, trend_type, record_date),
    INDEX idx_trend_type (trend_type),
    INDEX idx_record_date (record_date),
    INDEX idx_ranking_position (ranking_position),
    INDEX idx_consecutive_rise (consecutive_rise_days),
    INDEX idx_consecutive_fall (consecutive_fall_days),
    INDEX idx_trend_signal (trend_signal)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格趋势排名表';

-- 10. 价格趋势历史表
CREATE TABLE IF NOT EXISTS price_trend_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID',
    trend_type ENUM('RISE', 'FALL') NOT NULL COMMENT '趋势类型',
    ranking_position INT NOT NULL COMMENT '历史排名位置',
    price_change DECIMAL(10,2) NOT NULL COMMENT '价格变化',
    change_percentage DECIMAL(5,2) NOT NULL COMMENT '变化百分比',
    record_date DATE NOT NULL COMMENT '记录日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category_id (category_id),
    INDEX idx_trend_type (trend_type),
    INDEX idx_record_date (record_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格趋势历史表';

-- 11. 数据同步记录表
CREATE TABLE IF NOT EXISTS sync_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sync_type VARCHAR(20) NOT NULL COMMENT '同步类型：CATEGORY, PRICE',
    api_url VARCHAR(255) NOT NULL COMMENT 'API地址',
    request_params JSON COMMENT '请求参数',
    response_data JSON COMMENT '响应数据（截取前1000字符）',
    sync_status TINYINT NOT NULL COMMENT '同步状态：1-成功，0-失败',
    error_message TEXT COMMENT '错误信息',
    records_count INT DEFAULT 0 COMMENT '同步记录数',
    duration_ms INT COMMENT '耗时（毫秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sync_type (sync_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步记录表';

-- 12. 微信小程序配置表
CREATE TABLE IF NOT EXISTS miniprogram_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_type ENUM(
        'CAROUSEL',      -- 轮播图
        'ANNOUNCEMENT',  -- 公告
        'CONTACT_PHONE', -- 电话联系
        'WECHAT_COPY',   -- 复制微信号
        'SHARE_CONFIG',  -- 一键分享配置
        'SYSTEM_CONFIG'  -- 系统配置
    ) NOT NULL COMMENT '配置类型',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键名',
    config_name VARCHAR(200) NOT NULL COMMENT '配置名称',
    config_value JSON NOT NULL COMMENT '配置值，JSON格式',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    start_time DATETIME DEFAULT NULL COMMENT '生效开始时间',
    end_time DATETIME DEFAULT NULL COMMENT '生效结束时间',
    group_name VARCHAR(100) DEFAULT NULL COMMENT '分组名称',
    tags JSON DEFAULT NULL COMMENT '标签数组',
    description TEXT COMMENT '配置描述',
    extra_data JSON DEFAULT NULL COMMENT '扩展数据',
    created_by VARCHAR(50) DEFAULT 'system' COMMENT '创建者',
    updated_by VARCHAR(50) DEFAULT 'system' COMMENT '更新者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_config_type_key (config_type, config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_display_order (display_order),
    INDEX idx_group_name (group_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序配置表';

-- 13. 微信小程序配置日志表
CREATE TABLE IF NOT EXISTS miniprogram_config_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    config_id INT NOT NULL COMMENT '配置ID',
    action ENUM('CREATE', 'UPDATE', 'DELETE', 'ENABLE', 'DISABLE') NOT NULL COMMENT '操作类型',
    old_value JSON COMMENT '旧值',
    new_value JSON COMMENT '新值',
    operator VARCHAR(50) NOT NULL COMMENT '操作者',
    operator_type ENUM('ADMIN', 'SYSTEM') DEFAULT 'ADMIN' COMMENT '操作者类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_config_id (config_id),
    INDEX idx_action (action),
    INDEX idx_operator (operator),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (config_id) REFERENCES miniprogram_configs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序配置日志表';

-- 14. 微信小程序联系人表
CREATE TABLE IF NOT EXISTS miniprogram_contacts (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '联系人ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID，关联users表',
    name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号码',
    wechat_id VARCHAR(100) COMMENT '微信号',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    category ENUM('FRIEND', 'FAMILY', 'COLLEAGUE', 'BUSINESS', 'OTHER') DEFAULT 'FRIEND' COMMENT '联系人分类',
    tags JSON COMMENT '标签数组',
    company VARCHAR(200) COMMENT '公司名称',
    position VARCHAR(100) COMMENT '职位',
    email VARCHAR(100) COMMENT '邮箱地址',
    address TEXT COMMENT '地址',
    birthday DATE COMMENT '生日',
    notes TEXT COMMENT '备注信息',
    is_favorite TINYINT(1) DEFAULT 0 COMMENT '是否收藏',
    is_blocked TINYINT(1) DEFAULT 0 COMMENT '是否屏蔽',
    privacy_level ENUM('PUBLIC', 'FRIENDS', 'PRIVATE') DEFAULT 'FRIENDS' COMMENT '隐私级别',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_phone (phone),
    INDEX idx_wechat_id (wechat_id),
    INDEX idx_category (category),
    INDEX idx_is_favorite (is_favorite),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序联系人表';

-- 15. 联系人分享配置表
CREATE TABLE IF NOT EXISTS contact_share_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分享配置ID',
    contact_id INT NOT NULL COMMENT '联系人ID',
    share_type ENUM('QR_CODE', 'LINK', 'CARD') NOT NULL COMMENT '分享类型',
    share_title VARCHAR(200) COMMENT '分享标题',
    share_description TEXT COMMENT '分享描述',
    share_image_url VARCHAR(500) COMMENT '分享图片URL',
    share_url VARCHAR(500) COMMENT '分享链接',
    qr_code_data TEXT COMMENT '二维码数据',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    expire_time DATETIME COMMENT '过期时间',
    access_password VARCHAR(20) COMMENT '访问密码',
    max_access_count INT DEFAULT 0 COMMENT '最大访问次数，0表示无限制',
    current_access_count INT DEFAULT 0 COMMENT '当前访问次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_contact_id (contact_id),
    INDEX idx_share_type (share_type),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_expire_time (expire_time),
    FOREIGN KEY (contact_id) REFERENCES miniprogram_contacts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人分享配置表';

-- 16. 联系人访问记录表
CREATE TABLE IF NOT EXISTS contact_access_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    contact_id INT NOT NULL COMMENT '联系人ID',
    share_config_id INT COMMENT '分享配置ID',
    visitor_user_id VARCHAR(50) COMMENT '访问者用户ID',
    visitor_ip VARCHAR(45) COMMENT '访问者IP',
    access_type ENUM('VIEW', 'SHARE', 'DOWNLOAD', 'SAVE') NOT NULL COMMENT '访问类型',
    user_agent TEXT COMMENT '用户代理信息',
    referer VARCHAR(500) COMMENT '来源页面',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    INDEX idx_contact_id (contact_id),
    INDEX idx_share_config_id (share_config_id),
    INDEX idx_visitor_user_id (visitor_user_id),
    INDEX idx_access_type (access_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (contact_id) REFERENCES miniprogram_contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (share_config_id) REFERENCES contact_share_configs(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人访问记录表';

-- 插入初始化数据和日志
INSERT IGNORE INTO operation_logs (user_type, action, resource, details, ip_address, created_at) VALUES
('SYSTEM', 'DATABASE_INIT', 'ALL_TABLES', '{"action": "create_all_tables", "timestamp": "2025-06-22", "tables_count": 16}', '127.0.0.1', NOW()),
('SYSTEM', 'SYSTEM_READY', 'DATABASE', '{"message": "数据库表结构初始化完成", "timestamp": "2025-06-22"}', '127.0.0.1', NOW());

-- 显示创建结果
SELECT '数据库表结构初始化完成！' as message;
SHOW TABLES;
