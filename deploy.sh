#!/bin/bash

# 宝丰后台管理系统部署脚本
# 使用方法: ./deploy.sh [environment]
# 环境选项: development, production (默认: production)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
ENVIRONMENT=${1:-production}
PROJECT_NAME="baofeng-rd"
PROJECT_DIR=$(pwd)
NODE_VERSION="18"
PM2_APP_NAME="baofeng-rd-api"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查 Node.js 版本
check_node_version() {
    if command -v node &> /dev/null; then
        NODE_CURRENT=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_CURRENT" -lt "$NODE_VERSION" ]; then
            log_warning "当前 Node.js 版本为 v$NODE_CURRENT，建议使用 v$NODE_VERSION 或更高版本"
        else
            log_success "Node.js 版本检查通过: $(node -v)"
        fi
    else
        log_error "Node.js 未安装"
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    check_command "node"
    check_command "npm"
    check_command "mysql"
    
    check_node_version
    
    # 检查 PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，正在安装..."
        npm install -g pm2
        log_success "PM2 安装完成"
    else
        log_success "PM2 已安装: $(pm2 -v)"
    fi
    
    # 检查 Nginx（可选）
    if command -v nginx &> /dev/null; then
        log_success "Nginx 已安装: $(nginx -v 2>&1)"
    else
        log_warning "Nginx 未安装，建议安装用于反向代理"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p temp
    mkdir -p backups
    
    log_success "目录创建完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 清理 node_modules（生产环境）
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "清理现有依赖..."
        rm -rf node_modules
        rm -f package-lock.json
    fi
    
    # 安装依赖
    npm ci --only=production
    
    # 安装开发依赖（用于构建）
    npm install --only=dev
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 清理旧的构建文件
    npm run clean
    
    # 构建 TypeScript
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist 目录不存在"
        exit 1
    fi
    
    log_success "项目构建完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        if [ ! -f ".env.production" ]; then
            log_error ".env.production 文件不存在，请先创建并配置"
            exit 1
        fi
        
        # 复制生产环境配置
        cp .env.production .env
        log_success "生产环境配置已应用"
    else
        if [ ! -f ".env" ]; then
            log_warning ".env 文件不存在，使用默认配置"
            cp .env.example .env 2>/dev/null || true
        fi
        log_success "开发环境配置已应用"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 运行数据库初始化脚本
    if [ -f "dist/scripts/init-database.js" ]; then
        node dist/scripts/init-database.js
        log_success "数据库初始化完成"
    else
        log_warning "数据库初始化脚本不存在，跳过"
    fi
}

# 运行测试
run_tests() {
    if [ "$ENVIRONMENT" = "development" ]; then
        log_info "运行测试..."
        npm test
        log_success "测试通过"
    else
        log_info "生产环境部署，跳过测试"
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止现有的 PM2 进程
    pm2 stop $PM2_APP_NAME 2>/dev/null || true
    pm2 delete $PM2_APP_NAME 2>/dev/null || true
    
    # 启动服务
    if [ "$ENVIRONMENT" = "production" ]; then
        pm2 start ecosystem.config.js --env production
    else
        pm2 start ecosystem.config.js --env development
    fi
    
    # 保存 PM2 配置
    pm2 save
    
    # 设置 PM2 开机自启
    pm2 startup
    
    log_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务启动
    sleep 5
    
    # 检查 PM2 状态
    pm2 status
    
    # 检查服务健康状态
    if command -v curl &> /dev/null; then
        local health_url="http://localhost:3000/api/health"
        if curl -f -s "$health_url" > /dev/null; then
            log_success "服务健康检查通过"
        else
            log_warning "服务健康检查失败，请检查日志"
        fi
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=================================="
    echo "  部署完成！"
    echo "=================================="
    echo "环境: $ENVIRONMENT"
    echo "项目目录: $PROJECT_DIR"
    echo "API 地址: http://localhost:3000/api"
    echo "API 文档: http://localhost:3000/api-docs"
    echo ""
    echo "常用命令:"
    echo "  查看日志: pm2 logs $PM2_APP_NAME"
    echo "  重启服务: pm2 restart $PM2_APP_NAME"
    echo "  停止服务: pm2 stop $PM2_APP_NAME"
    echo "  查看状态: pm2 status"
    echo ""
    echo "日志文件位置:"
    echo "  应用日志: $PROJECT_DIR/logs/app.log"
    echo "  PM2 日志: ~/.pm2/logs/"
    echo ""
}

# 主函数
main() {
    log_info "开始部署 $PROJECT_NAME ($ENVIRONMENT 环境)..."
    
    # 检查依赖
    check_dependencies
    
    # 创建目录
    create_directories
    
    # 配置环境
    setup_environment
    
    # 安装依赖
    install_dependencies
    
    # 构建项目
    build_project
    
    # 初始化数据库
    init_database
    
    # 运行测试
    run_tests
    
    # 启动服务
    start_services
    
    # 验证部署
    verify_deployment
    
    # 显示部署信息
    show_deployment_info
    
    log_success "部署完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
