#!/usr/bin/env node

/**
 * 验证数据库迁移是否成功
 */

const mysql = require('mysql2/promise');

async function verifyMigration() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '', // 请根据实际情况修改密码
      database: 'baofeng_recycle'
    });

    console.log('✅ 数据库连接成功');

    // 检查字段是否存在
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'baofeng_recycle' 
      AND TABLE_NAME = 'price_trend_rankings' 
      AND COLUMN_NAME IN ('consecutive_rise_days', 'consecutive_fall_days', 'trend_signal')
      ORDER BY COLUMN_NAME
    `);

    console.log('\n📋 字段检查结果:');
    if (columns.length === 0) {
      console.log('❌ 连续涨跌字段不存在，需要执行数据库迁移');
      console.log('\n请执行以下命令:');
      console.log('mysql -u root -p baofeng_recycle < migrate_consecutive_fields.sql');
    } else {
      console.log('✅ 找到以下字段:');
      columns.forEach(col => {
        console.log(`   - ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT}`);
      });

      if (columns.length === 3) {
        console.log('\n✅ 所有连续涨跌字段都已存在');
      } else {
        console.log('\n⚠️  部分字段缺失，请检查迁移脚本');
      }
    }

    // 检查索引是否存在
    const [indexes] = await connection.execute(`
      SELECT INDEX_NAME, COLUMN_NAME
      FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE TABLE_SCHEMA = 'baofeng_recycle' 
      AND TABLE_NAME = 'price_trend_rankings' 
      AND INDEX_NAME IN ('idx_consecutive_rise', 'idx_consecutive_fall', 'idx_trend_signal')
      ORDER BY INDEX_NAME
    `);

    console.log('\n📊 索引检查结果:');
    if (indexes.length === 0) {
      console.log('❌ 连续涨跌相关索引不存在');
    } else {
      console.log('✅ 找到以下索引:');
      indexes.forEach(idx => {
        console.log(`   - ${idx.INDEX_NAME} on ${idx.COLUMN_NAME}`);
      });
    }

    // 检查现有数据
    const [dataCount] = await connection.execute(`
      SELECT COUNT(*) as total_count
      FROM price_trend_rankings
    `);

    console.log(`\n📈 数据统计: 共有 ${dataCount[0].total_count} 条价格涨跌榜记录`);

    if (columns.length === 3 && dataCount[0].total_count > 0) {
      const [sampleData] = await connection.execute(`
        SELECT category_name, consecutive_rise_days, consecutive_fall_days, trend_signal
        FROM price_trend_rankings 
        ORDER BY id DESC 
        LIMIT 3
      `);

      console.log('\n📝 最新数据示例:');
      sampleData.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.category_name}: 连续涨${row.consecutive_rise_days}天, 连续跌${row.consecutive_fall_days}天, 信号: ${row.trend_signal}`);
      });
    }

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 请检查数据库连接信息（用户名、密码）');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 请确保 MySQL 服务正在运行');
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行验证
verifyMigration();
