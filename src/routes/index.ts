import { Router } from 'express';
import authRoutes from './auth';
import userRoutes from './users';
import adminAuthRoutes from './adminAuth';
import categoriesV2Routes from './categoriesV2';
import wechatRoutes from './wechat';
import contactRoutes from './contacts';
import exportRoutes from './export';
import recycleStoreRoutes from './recycleStoreRoutes';
import miniprogramConfigRoutes from './miniprogramConfigRoutes';
import { sendSuccess } from '../utils/response';
import { AuthController } from '../controllers/authController';

const router = Router();

// Health check endpoint with database status
router.get('/health', async (req, res) => {
  try {
    const { db } = await import('../config/database');

    // Test database connection
    let dbStatus = 'OK';
    let dbError = null;
    try {
      await db.query('SELECT 1');
    } catch (error: any) {
      dbStatus = 'ERROR';
      dbError = error.message;
    }

    const healthData = {
      status: dbStatus === 'OK' ? 'OK' : 'DEGRADED',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      database: {
        status: dbStatus,
        error: dbError
      },
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024)
      }
    };

    const statusCode = dbStatus === 'OK' ? 200 : 503;
    const message = dbStatus === 'OK' ? 'Service is healthy' : 'Service is degraded';

    res.status(statusCode);
    sendSuccess(res, healthData, message);
  } catch (error: any) {
    res.status(503);
    sendSuccess(res, {
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      error: error.message
    }, 'Service is unhealthy');
  }
});

// API routes
// 注意：具体的路由必须在通用路由之前定义
// 通用用户信息接口 (支持管理员和普通用户)
router.get('/auth/user', AuthController.getCurrentUser);
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/admin/auth', adminAuthRoutes);
router.use('/categories', categoriesV2Routes);
router.use('/wechat', wechatRoutes);
router.use('/contacts', contactRoutes);
router.use('/export', exportRoutes);
router.use('/recycle-stores', recycleStoreRoutes);
router.use('/miniprogram/configs', miniprogramConfigRoutes);

export default router;
