#!/usr/bin/env node

/**
 * 价格涨跌榜计算脚本
 * 每天定时运行，计算价格涨跌榜数据
 * 
 * 使用方法:
 * npm run calculate-rankings
 * 或者
 * node dist/scripts/calculatePriceRankings.js
 */

import db from '../config/knex';
import logger from '../config/logger';

interface PriceChangeData {
  category_id: number;
  category_level: number;
  category_name: string;
  brand_name?: string;
  model_name?: string;
  sub_model_name?: string;
  memory_size?: string;
  tag_name?: string;
  group_name?: string;
  current_price: number;
  previous_price: number;
  price_change: number;
  change_percentage: number;
  trend_type: 'RISE' | 'FALL' | 'STABLE';
  consecutive_rise_days: number;
  consecutive_fall_days: number;
  trend_signal: string;
}

class PriceRankingCalculator {
  private readonly STABLE_THRESHOLD = 0.01; // 1% 以内认为是稳定
  private readonly TOP_RANKINGS_COUNT = 50; // 每个榜单显示前50名

  /**
   * 主执行函数
   */
  async execute(): Promise<void> {
    try {
      logger.info('开始计算价格涨跌榜...');
      
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const todayStr = today.toISOString().split('T')[0]!;
      const yesterdayStr = yesterday.toISOString().split('T')[0]!;

      // 1. 获取价格变化数据
      const priceChanges = await this.getPriceChanges(todayStr, yesterdayStr);
      logger.info(`获取到 ${priceChanges.length} 条价格变化数据`);

      if (priceChanges.length === 0) {
        logger.warn('没有找到价格变化数据，跳过榜单计算');
        return;
      }

      // 2. 清理今日已存在的榜单数据
      await this.clearTodayRankings(todayStr);

      // 3. 计算涨幅榜
      const riseRankings = this.calculateRiseRankings(priceChanges);
      await this.saveRankings(riseRankings, todayStr, 'RISE_RANKING');
      logger.info(`保存了 ${riseRankings.length} 条涨幅榜数据`);

      // 4. 计算跌幅榜
      const fallRankings = this.calculateFallRankings(priceChanges);
      await this.saveRankings(fallRankings, todayStr, 'FALL_RANKING');
      logger.info(`保存了 ${fallRankings.length} 条跌幅榜数据`);

      // 5. 计算连续涨跌天数
      await this.calculateConsecutiveTrends(todayStr);
      logger.info('计算连续涨跌天数完成');

      // 6. 更新趋势历史数据
      await this.updateTrendHistory(todayStr);
      logger.info('更新趋势历史数据完成');

      // 7. 记录操作日志
      await this.logOperation(todayStr, riseRankings.length + fallRankings.length);

      logger.info('价格涨跌榜计算完成');

    } catch (error) {
      logger.error('计算价格涨跌榜时发生错误:', error);
      throw error;
    }
  }

  /**
   * 获取价格变化数据
   * 简化版本：基于现有测试数据生成模拟的价格变化
   */
  private async getPriceChanges(todayStr: string, yesterdayStr: string): Promise<PriceChangeData[]> {
    logger.info('正在生成模拟价格变化数据...');

    // 检查是否已有今日数据，如果有则跳过
    const existingData = await db('price_trend_rankings')
      .where('ranking_date', todayStr)
      .first();

    if (existingData) {
      logger.info('今日涨跌榜数据已存在，跳过计算');
      return [];
    }

    // 生成一些模拟的价格变化数据
    const mockData: PriceChangeData[] = [
      {
        category_id: 101,
        category_level: 3,
        category_name: 'iPhone 15 Pro',
        brand_name: '苹果',
        model_name: 'iPhone 15',
        sub_model_name: 'iPhone 15 Pro',
        memory_size: '128GB',
        tag_name: '靓机',
        group_name: '成色',
        current_price: 7999.00,
        previous_price: 7799.00,
        price_change: 200.00,
        change_percentage: 2.56,
        trend_type: 'RISE',
        consecutive_rise_days: 0,
        consecutive_fall_days: 0,
        trend_signal: 'NORMAL'
      },
      {
        category_id: 102,
        category_level: 3,
        category_name: 'Galaxy S24',
        brand_name: '三星',
        model_name: 'Galaxy S24',
        sub_model_name: 'Galaxy S24',
        memory_size: '256GB',
        tag_name: '靓机',
        group_name: '成色',
        current_price: 6999.00,
        previous_price: 6799.00,
        price_change: 200.00,
        change_percentage: 2.94,
        trend_type: 'RISE',
        consecutive_rise_days: 0,
        consecutive_fall_days: 0,
        trend_signal: 'NORMAL'
      },
      {
        category_id: 103,
        category_level: 3,
        category_name: 'Mate 60',
        brand_name: '华为',
        model_name: 'Mate 60',
        sub_model_name: 'Mate 60',
        memory_size: '256GB',
        tag_name: '靓机',
        group_name: '成色',
        current_price: 5499.00,
        previous_price: 5899.00,
        price_change: -400.00,
        change_percentage: -6.78,
        trend_type: 'FALL',
        consecutive_rise_days: 0,
        consecutive_fall_days: 0,
        trend_signal: 'NORMAL'
      },
      {
        category_id: 104,
        category_level: 3,
        category_name: 'Xiaomi 14',
        brand_name: '小米',
        model_name: 'Xiaomi 14',
        sub_model_name: 'Xiaomi 14',
        memory_size: '256GB',
        tag_name: '靓机',
        group_name: '成色',
        current_price: 4299.00,
        previous_price: 4599.00,
        price_change: -300.00,
        change_percentage: -6.52,
        trend_type: 'FALL',
        consecutive_rise_days: 0,
        consecutive_fall_days: 0,
        trend_signal: 'NORMAL'
      },
      {
        category_id: 105,
        category_level: 3,
        category_name: 'OPPO Find X7',
        brand_name: 'OPPO',
        model_name: 'Find X7',
        sub_model_name: 'OPPO Find X7',
        memory_size: '512GB',
        tag_name: '靓机',
        group_name: '成色',
        current_price: 3999.00,
        previous_price: 3899.00,
        price_change: 100.00,
        change_percentage: 2.56,
        trend_type: 'RISE',
        consecutive_rise_days: 0,
        consecutive_fall_days: 0,
        trend_signal: 'NORMAL'
      }
    ];

    logger.info(`生成了 ${mockData.length} 条模拟价格变化数据`);
    return mockData;
  }

  /**
   * 计算涨幅榜
   */
  private calculateRiseRankings(priceChanges: PriceChangeData[]): PriceChangeData[] {
    return priceChanges
      .filter(item => item.trend_type === 'RISE')
      .sort((a, b) => b.change_percentage - a.change_percentage)
      .slice(0, this.TOP_RANKINGS_COUNT);
  }

  /**
   * 计算跌幅榜
   */
  private calculateFallRankings(priceChanges: PriceChangeData[]): PriceChangeData[] {
    return priceChanges
      .filter(item => item.trend_type === 'FALL')
      .sort((a, b) => a.change_percentage - b.change_percentage) // 跌幅从大到小（负数）
      .slice(0, this.TOP_RANKINGS_COUNT);
  }

  /**
   * 清理今日已存在的榜单数据
   */
  private async clearTodayRankings(todayStr: string): Promise<void> {
    await db('price_trend_rankings')
      .where('ranking_date', todayStr)
      .del();
  }

  /**
   * 保存榜单数据
   */
  private async saveRankings(
    rankings: PriceChangeData[],
    todayStr: string,
    rankingType: 'RISE_RANKING' | 'FALL_RANKING'
  ): Promise<void> {
    if (rankings.length === 0) return;

    // 检查是否存在新字段
    const hasConsecutiveFields = await this.checkConsecutiveFieldsExist();

    const rankingData = rankings.map((item, index) => {
      const baseData = {
        ranking_date: todayStr,
        category_id: item.category_id,
        category_level: item.category_level,
        category_name: item.category_name,
        brand_name: item.brand_name,
        model_name: item.model_name,
        sub_model_name: item.sub_model_name,
        memory_size: item.memory_size,
        tag_name: item.tag_name,
        group_name: item.group_name,
        current_price: item.current_price,
        previous_price: item.previous_price,
        price_change: item.price_change,
        change_percentage: item.change_percentage,
        trend_type: item.trend_type,
        ranking_position: index + 1,
        ranking_type: rankingType,
        status: 1,
        created_at: db.fn.now(),
        updated_at: db.fn.now()
      };

      // 只有当字段存在时才添加连续涨跌字段
      if (hasConsecutiveFields) {
        return {
          ...baseData,
          consecutive_rise_days: 0, // 初始值，稍后会更新
          consecutive_fall_days: 0, // 初始值，稍后会更新
          trend_signal: 'NORMAL' // 初始值，稍后会更新
        };
      }

      return baseData;
    });

    await db('price_trend_rankings').insert(rankingData);
  }

  /**
   * 检查连续涨跌字段是否存在
   */
  private async checkConsecutiveFieldsExist(): Promise<boolean> {
    try {
      const result = await db.raw(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'baofeng_recycle'
        AND TABLE_NAME = 'price_trend_rankings'
        AND COLUMN_NAME IN ('consecutive_rise_days', 'consecutive_fall_days', 'trend_signal')
      `);

      const columns = result[0] || [];
      return columns.length === 3; // 所有三个字段都存在
    } catch (error) {
      logger.warn('检查连续涨跌字段时发生错误，假设字段不存在:', error);
      return false;
    }
  }

  /**
   * 更新趋势历史数据
   */
  private async updateTrendHistory(todayStr: string): Promise<void> {
    // 这里可以实现更复杂的趋势分析逻辑
    // 暂时先记录基本信息
    logger.info('趋势历史数据更新功能待实现');
  }

  /**
   * 计算连续涨跌天数
   */
  private async calculateConsecutiveTrends(todayStr: string): Promise<void> {
    try {
      // 检查是否存在连续涨跌字段
      const hasConsecutiveFields = await this.checkConsecutiveFieldsExist();

      if (!hasConsecutiveFields) {
        logger.warn('连续涨跌字段不存在，跳过连续涨跌计算。请先执行数据库迁移脚本。');
        return;
      }

      // 获取今日所有榜单数据
      const todayRankings = await db('price_trend_rankings')
        .where('ranking_date', todayStr)
        .where('status', 1);

      for (const ranking of todayRankings) {
        const consecutiveDays = await this.getConsecutiveTrendDays(
          ranking.category_id,
          ranking.trend_type,
          todayStr
        );

        let consecutiveRiseDays = 0;
        let consecutiveFallDays = 0;
        let trendSignal = 'NORMAL';

        if (ranking.trend_type === 'RISE') {
          consecutiveRiseDays = consecutiveDays;
          if (consecutiveDays >= 2) {
            trendSignal = `CONSECUTIVE_RISE_${consecutiveDays}`;
          }
        } else if (ranking.trend_type === 'FALL') {
          consecutiveFallDays = consecutiveDays;
          if (consecutiveDays >= 2) {
            trendSignal = `CONSECUTIVE_FALL_${consecutiveDays}`;
          }
        }

        // 更新记录
        await db('price_trend_rankings')
          .where('id', ranking.id)
          .update({
            consecutive_rise_days: consecutiveRiseDays,
            consecutive_fall_days: consecutiveFallDays,
            trend_signal: trendSignal,
            updated_at: db.fn.now()
          });
      }

      logger.info(`更新了 ${todayRankings.length} 条记录的连续涨跌天数`);
    } catch (error) {
      logger.error('计算连续涨跌天数时发生错误:', error);
      throw error;
    }
  }

  /**
   * 获取指定分类的连续涨跌天数
   */
  private async getConsecutiveTrendDays(
    categoryId: number,
    currentTrendType: string,
    todayStr: string
  ): Promise<number> {
    if (currentTrendType === 'STABLE') {
      return 0;
    }

    let consecutiveDays = 1; // 包含今天
    let checkDate = new Date(todayStr);

    // 向前查找连续的相同趋势
    for (let i = 1; i <= 30; i++) { // 最多查找30天
      checkDate.setDate(checkDate.getDate() - 1);
      const checkDateStr = checkDate.toISOString().split('T')[0];

      const previousRecord = await db('price_trend_rankings')
        .where('category_id', categoryId)
        .where('ranking_date', checkDateStr)
        .where('trend_type', currentTrendType)
        .where('status', 1)
        .first();

      if (previousRecord) {
        consecutiveDays++;
      } else {
        break;
      }
    }

    return consecutiveDays;
  }

  /**
   * 记录操作日志
   */
  private async logOperation(todayStr: string, totalRecords: number): Promise<void> {
    await db('operation_logs').insert({
      operation_type: 'CALCULATE_PRICE_RANKINGS',
      table_name: 'price_trend_rankings',
      operation_data: JSON.stringify({
        ranking_date: todayStr,
        total_records: totalRecords,
        timestamp: new Date().toISOString()
      }),
      operator_id: 'system',
      operator_name: '系统定时任务',
      created_at: db.fn.now()
    });
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const calculator = new PriceRankingCalculator();
  calculator.execute()
    .then(() => {
      logger.info('价格涨跌榜计算任务完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('价格涨跌榜计算任务失败:', error);
      process.exit(1);
    });
}

export default PriceRankingCalculator;
