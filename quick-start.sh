#!/bin/bash

# 宝丰后台管理系统 - 快速启动脚本
# 适用于开发环境快速部署和测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Node.js
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt "18" ]; then
        log_warning "当前 Node.js 版本为 v$NODE_VERSION，建议使用 v18+"
    fi
    
    log_success "Node.js 版本: $(node -v)"
}

# 检查 MySQL
check_mysql() {
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL 未安装，请先安装 MySQL"
        exit 1
    fi
    
    log_success "MySQL 已安装"
}

# 安装依赖
install_deps() {
    log_info "安装项目依赖..."
    npm install
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    npm run build
    log_success "项目构建完成"
}

# 设置环境变量
setup_env() {
    if [ ! -f ".env" ]; then
        log_info "创建环境配置文件..."
        cp .env.production .env
        
        # 修改为开发环境默认配置
        sed -i.bak 's/NODE_ENV=production/NODE_ENV=development/' .env
        sed -i.bak 's/HOST=0.0.0.0/HOST=localhost/' .env
        sed -i.bak 's/DB_USER=baofeng_user/DB_USER=root/' .env
        sed -i.bak 's/DB_PASSWORD=your_secure_database_password_here/DB_PASSWORD=/' .env
        
        rm .env.bak 2>/dev/null || true
        
        log_success "环境配置文件已创建"
        log_warning "请根据需要修改 .env 文件中的数据库配置"
    else
        log_info "环境配置文件已存在"
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库..."
    
    # 读取环境变量
    source .env
    
    # 创建数据库
    mysql -u${DB_USER} -p${DB_PASSWORD} -e "CREATE DATABASE IF NOT EXISTS ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || {
        log_warning "数据库创建失败，可能已存在或权限不足"
    }
    
    log_success "数据库准备完成"
}

# 初始化数据库表
init_database() {
    log_info "初始化数据库表..."
    npm run init-db:prod
    log_success "数据库表初始化完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 检查端口是否被占用
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 3000 已被占用，尝试停止现有服务..."
        pkill -f "node.*3000" || true
        sleep 2
    fi
    
    # 启动服务
    npm start &
    SERVICE_PID=$!
    
    log_info "服务启动中，PID: $SERVICE_PID"
    
    # 等待服务启动
    log_info "等待服务启动..."
    for i in {1..30}; do
        if curl -f -s http://localhost:3000/health > /dev/null 2>&1; then
            log_success "服务启动成功！"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "服务启动超时"
            exit 1
        fi
        
        sleep 1
        echo -n "."
    done
    echo ""
}

# 显示服务信息
show_info() {
    echo ""
    echo "=================================="
    echo "  🎉 快速启动完成！"
    echo "=================================="
    echo "服务地址: http://localhost:3000"
    echo "API 文档: http://localhost:3000/api-docs"
    echo "健康检查: http://localhost:3000/health"
    echo ""
    echo "常用命令:"
    echo "  停止服务: pkill -f 'node.*3000'"
    echo "  查看日志: tail -f logs/app.log"
    echo "  重新构建: npm run build"
    echo ""
    echo "开发模式:"
    echo "  npm run dev  # 启动开发模式（自动重启）"
    echo ""
}

# 主函数
main() {
    echo "🚀 宝丰后台管理系统 - 快速启动"
    echo "=================================="
    
    # 检查系统依赖
    check_node
    check_mysql
    
    # 安装和构建
    install_deps
    build_project
    
    # 配置环境
    setup_env
    
    # 数据库设置
    create_database
    init_database
    
    # 启动服务
    start_service
    
    # 显示信息
    show_info
}

# 清理函数
cleanup() {
    log_info "正在清理..."
    if [ ! -z "$SERVICE_PID" ]; then
        kill $SERVICE_PID 2>/dev/null || true
    fi
}

# 设置清理陷阱
trap cleanup EXIT

# 执行主函数
main "$@"
