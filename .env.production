# 宝丰后台管理系统 - 生产环境配置
# 请根据实际生产环境修改相应的值

# 应用配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 数据库配置 - 请修改为生产环境的数据库信息
DB_HOST=localhost
DB_PORT=3306
DB_USER=baofeng_user
DB_PASSWORD=your_secure_database_password_here
DB_NAME=baofeng_recycle
DB_CHARSET=utf8mb4

# JWT 配置 - 请使用强密钥
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long-for-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 微信小程序配置 - 请填入真实的微信小程序信息
WECHAT_APP_ID=wx3263a06336cf0b6b
WECHAT_APP_SECRET=825d2600516af087542644467cf25fb8

# API 配置
API_VERSION=v1
API_PREFIX=/api

# 限流配置 - 生产环境建议更严格的限制
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS 配置 - 请设置为实际的前端域名
CORS_ORIGIN=https://your-frontend-domain.com,https://your-admin-domain.com

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
BCRYPT_ROUNDS=12

# 邮件配置 - 如果需要邮件功能
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-app-password

# SSL/TLS 配置（如果使用 HTTPS）
# SSL_CERT_PATH=/path/to/your/certificate.crt
# SSL_KEY_PATH=/path/to/your/private.key

# 监控和性能配置
# SENTRY_DSN=https://<EMAIL>/project-id
# REDIS_URL=redis://localhost:6379

# 文件上传配置
# UPLOAD_MAX_SIZE=10485760
# UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# 备份配置
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
