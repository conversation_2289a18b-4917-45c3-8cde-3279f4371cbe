-- 添加连续涨跌天数字段到 price_trend_rankings 表
-- 请在 MySQL 中执行此脚本

USE baofeng_recycle;

-- 添加连续涨跌天数字段
ALTER TABLE price_trend_rankings 
ADD COLUMN consecutive_rise_days INT DEFAULT 0 COMMENT '连续上涨天数（0表示今日未涨或中断）';

ALTER TABLE price_trend_rankings 
ADD COLUMN consecutive_fall_days INT DEFAULT 0 COMMENT '连续下跌天数（0表示今日未跌或中断）';

ALTER TABLE price_trend_rankings 
ADD COLUMN trend_signal VARCHAR(20) DEFAULT NULL COMMENT '趋势信号：CONSECUTIVE_RISE_2+, CONSECUTIVE_FALL_2+, NORMAL';

-- 添加索引以提高查询性能
CREATE INDEX idx_consecutive_rise ON price_trend_rankings(consecutive_rise_days);
CREATE INDEX idx_consecutive_fall ON price_trend_rankings(consecutive_fall_days);
CREATE INDEX idx_trend_signal ON price_trend_rankings(trend_signal);

-- 更新现有数据的趋势信号为 NORMAL
UPDATE price_trend_rankings SET trend_signal = 'NORMAL' WHERE trend_signal IS NULL;

-- 验证字段是否添加成功
DESCRIBE price_trend_rankings;
