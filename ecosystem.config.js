module.exports = {
  apps: [
    {
      name: 'baofeng-rd-api',
      script: 'dist/index.js',
      instances: 'max', // 使用所有CPU核心
      exec_mode: 'cluster', // 集群模式
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,

      // 日志配置
      log_file: 'logs/pm2-combined.log',
      out_file: 'logs/pm2-out.log',
      error_file: 'logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // 生产环境配置
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },

      // 开发环境配置
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000,
        instances: 1,
        exec_mode: 'fork'
      },

      // 健康检查
      health_check_grace_period: 3000,

      // 进程监控
      pmx: true,

      // 优雅关闭
      kill_timeout: 5000,
      listen_timeout: 3000,

      // 环境变量文件
      env_file: '.env.production'
    },

    {
      name: 'price-rankings-cron',
      script: 'dist/scripts/calculatePriceRankings.js',
      instances: 1,
      exec_mode: 'fork',
      autorestart: false,
      cron_restart: '0 8 * * *', // 每天早上 8:00 运行
      watch: false,
      max_memory_restart: '512M',

      // 日志配置
      log_file: 'logs/cron-combined.log',
      out_file: 'logs/cron-out.log',
      error_file: 'logs/cron-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

      env_production: {
        NODE_ENV: 'production'
      },

      env_development: {
        NODE_ENV: 'development'
      },

      // 环境变量文件
      env_file: '.env.production'
    },

    {
      name: 'database-init',
      script: 'dist/scripts/init-database.js',
      instances: 1,
      exec_mode: 'fork',
      autorestart: false,
      watch: false,

      // 仅在部署时运行一次
      env_production: {
        NODE_ENV: 'production'
      },

      // 环境变量文件
      env_file: '.env.production'
    }
  ],

  // 部署配置
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server-ip'],
      ref: 'origin/main',
      repo: '**************:your-username/baofeng-rd.git',
      path: '/var/www/baofeng-rd',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
