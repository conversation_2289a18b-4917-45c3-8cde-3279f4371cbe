# 宝丰后台管理系统 - 部署指南

## 📋 部署前准备

### 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **Node.js**: v18.0.0 或更高版本
- **MySQL**: v8.0 或更高版本
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间

### 必需软件安装

#### 1. Node.js 安装
```bash
# 使用 nvm 安装 Node.js 18
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
```

#### 2. MySQL 安装
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# macOS (使用 Homebrew)
brew install mysql
```

#### 3. PM2 安装
```bash
npm install -g pm2
```

#### 4. Nginx 安装（可选，用于反向代理）
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx

# macOS
brew install nginx
```

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone <your-repository-url>
cd baofeng-rd
```

### 2. 配置环境变量
```bash
# 复制生产环境配置模板
cp .env.production .env

# 编辑配置文件
nano .env
```

**重要配置项**:
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_NAME=baofeng_recycle

# JWT 密钥（必须修改）
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# CORS 配置
CORS_ORIGIN=https://your-frontend-domain.com
```

### 3. 创建数据库用户
```sql
-- 登录 MySQL
mysql -u root -p

-- 创建数据库和用户
CREATE DATABASE baofeng_recycle CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'baofeng_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON baofeng_recycle.* TO 'baofeng_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. 运行部署脚本
```bash
# 生产环境部署
./deploy.sh production

# 或者开发环境部署
./deploy.sh development
```

## 📝 手动部署步骤

如果自动部署脚本失败，可以按以下步骤手动部署：

### 1. 安装依赖
```bash
npm ci --only=production
npm install --only=dev  # 用于构建
```

### 2. 构建项目
```bash
npm run clean
npm run build
```

### 3. 初始化数据库
```bash
npm run init-db:prod
```

### 4. 启动服务
```bash
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 🔧 Nginx 配置

### 1. 复制配置文件
```bash
sudo cp nginx.conf /etc/nginx/sites-available/baofeng-rd
sudo ln -s /etc/nginx/sites-available/baofeng-rd /etc/nginx/sites-enabled/
```

### 2. 修改配置
编辑 `/etc/nginx/sites-available/baofeng-rd`:
- 修改 `server_name` 为你的域名
- 配置 SSL 证书路径
- 调整上游服务器配置

### 3. 测试并重启 Nginx
```bash
sudo nginx -t
sudo systemctl restart nginx
```

## 🔍 验证部署

### 1. 检查服务状态
```bash
pm2 status
pm2 logs baofeng-rd-api
```

### 2. 测试 API 接口
```bash
# 健康检查
curl http://localhost:3000/api/health

# API 文档
curl http://localhost:3000/api-docs
```

### 3. 检查数据库连接
```bash
# 查看应用日志
tail -f logs/app.log

# 查看 PM2 日志
pm2 logs baofeng-rd-api --lines 50
```

## 🛠️ 常用运维命令

### PM2 管理
```bash
# 查看状态
pm2 status

# 重启服务
pm2 restart baofeng-rd-api

# 停止服务
pm2 stop baofeng-rd-api

# 查看日志
pm2 logs baofeng-rd-api

# 监控
pm2 monit
```

### 数据库管理
```bash
# 重新初始化数据库
npm run init-db:prod

# 备份数据库
mysqldump -u baofeng_user -p baofeng_recycle > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
mysql -u baofeng_user -p baofeng_recycle < backup_file.sql
```

### 日志管理
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 清理日志
> logs/app.log
> logs/error.log
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL 证书配置
```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 定期备份
创建备份脚本 `/etc/cron.daily/baofeng-backup`:
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/baofeng"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
mysqldump -u baofeng_user -p'password' baofeng_recycle > $BACKUP_DIR/db_$DATE.sql
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /var/www/baofeng-rd

# 保留最近 7 天的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接配置
   - 检查防火墙设置

2. **PM2 服务启动失败**
   - 查看 PM2 日志: `pm2 logs`
   - 检查端口占用: `netstat -tlnp | grep 3000`
   - 验证环境变量配置

3. **Nginx 502 错误**
   - 检查后端服务状态
   - 验证 upstream 配置
   - 查看 Nginx 错误日志

### 日志位置
- 应用日志: `logs/app.log`
- PM2 日志: `~/.pm2/logs/`
- Nginx 日志: `/var/log/nginx/`
- MySQL 日志: `/var/log/mysql/`

## 📞 技术支持

如果在部署过程中遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件
3. 参考故障排除部分
4. 联系技术支持团队

---

**部署完成后，请及时修改默认密码和密钥，确保系统安全！**
